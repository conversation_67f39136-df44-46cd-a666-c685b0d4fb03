package com.saidigital.tmp.services.claim.dto;

import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class RebateClaimListWsDTO extends BasicSearchWsDTO<ClaimListItemWsDTO> {

    // Additional properties for backward compatibility with frontend
    @JsonProperty("items")
    public List<ClaimListItemWsDTO> getItems() {
        return this.getResults();
    }

    public void setItems(List<ClaimListItemWsDTO> items) {
        this.setResults(items);
    }

    public static RebateClaimListWsDTO create(
            List<ClaimListItemWsDTO> items,
            int currentPage,
            int pageSize,
            long totalResults,
            String sort) {
        
        int totalPages = (int) Math.ceil((double) totalResults / pageSize);
        
        PageableWsDTO pagination = PageableWsDTO.builder()
                .currentPage(currentPage)
                .pageSize(pageSize)
                .totalPages(totalPages)
                .totalResults(totalResults)
                .hasNext(currentPage < totalPages - 1)
                .hasPrevious(currentPage > 0)
                .sort(sort)
                .numberOfElements(items.size())
                .first(currentPage == 0)
                .last(currentPage >= totalPages - 1)
                .empty(items.isEmpty())
                .build();

        return RebateClaimListWsDTO.builder()
                .type("rebateClaimSearchPageWsDTO")
                .pagination(pagination)
                .results(items)
                .build();
    }
}
