package com.saidigital.tmp.services.claim.dto;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ClaimListItemWsDTO {

    @JsonProperty("id")
    private String id;

    @JsonProperty("dealer")
    private BasicDealerWsDTO dealer;

    @JsonProperty("claimableAmount")
    private PriceDataWsDTO claimableAmount;

    @JsonProperty("status")
    private EnumWsDTO status;

    @JsonProperty("updatedBy")
    private BasicEmployeeDataWsDTO updatedBy;

    @JsonProperty("createDate")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
    private Date createDate;

    @JsonProperty("updatedDate")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
    private Date updatedDate;

    @JsonProperty("referenceNumber")
    private String referenceNumber;

    @JsonProperty("dmcmReference")
    private String dmcmReference;

    @JsonProperty("sbReference")
    private String sbReference;

    @JsonProperty("sapDocNo")
    private String sapDocNo;

    @JsonProperty("clearingDocNo")
    private String clearingDocNo;

    @JsonProperty("orNumber")
    private String orNumber;

    @JsonProperty("exported")
    private Boolean exported;

    @JsonProperty("requiredDoc")
    private String requiredDoc;

    @JsonProperty("generalConfigVersion")
    private Integer generalConfigVersion;

    @JsonProperty("returnReason")
    private String returnReason;

    @JsonProperty("postedAccountingExported")
    private Boolean postedAccountingExported;

    // Additional backward compatibility fields
    @JsonProperty("createdDate")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
    private Date createdDate;

    @JsonProperty("lastUpdatedDate")
    @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
    private Date lastUpdatedDate;

    @JsonProperty("lastUpdatedBy")
    private String lastUpdatedBy;
}
