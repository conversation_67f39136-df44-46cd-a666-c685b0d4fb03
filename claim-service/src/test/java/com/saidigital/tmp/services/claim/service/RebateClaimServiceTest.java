package com.saidigital.tmp.services.claim.service;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

import java.util.Arrays;
import java.util.Date;
import java.util.Optional;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;

import com.saidigital.tmp.services.claim.dto.ClaimListItemWsDTO;
import com.saidigital.tmp.services.claim.dto.PageableWsDTO;
import com.saidigital.tmp.services.claim.dto.RebateClaimListWsDTO;
import com.saidigital.tmp.services.claim.dto.RebateClaimResponseDTO;
import com.saidigital.tmp.services.claim.enums.RebateClaimStatus;
import com.saidigital.tmp.services.claim.model.RebateClaim;
import com.saidigital.tmp.services.claim.repository.RebateClaimRepository;

@ExtendWith(MockitoExtension.class)
public class RebateClaimServiceTest {

    @Mock
    private RebateClaimRepository rebateClaimRepository;

    @InjectMocks
    private RebateClaimService rebateClaimService;

    private RebateClaim testClaim1;
    private RebateClaim testClaim2;

    @BeforeEach
    void setUp() {
        Date now = new Date();
        
        testClaim1 = RebateClaim.builder()
                .code("0030-CLM-001")
                .status(RebateClaimStatus.MARKETING_VALIDATED)
                .claimableAmount(1000.0)
                .dealer("0030")
                .createdBy("testuser")
                .updatedBy("testuser")
                .referenceNumber("REF001")
                .dmcmReference("DMCM001")
                .sbReference("SB001")
                .exported(false)
                .generalConfigVersion(1)
                .build();
        testClaim1.setCreatedTime(now);
        testClaim1.setUpdatedTime(now);

        testClaim2 = RebateClaim.builder()
                .code("0030-CLM-002")
                .status(RebateClaimStatus.RFP_PROCESSING)
                .claimableAmount(2000.0)
                .dealer("0030")
                .createdBy("testuser")
                .updatedBy("testuser")
                .referenceNumber("REF002")
                .dmcmReference("DMCM002")
                .sbReference("SB002")
                .exported(false)
                .generalConfigVersion(1)
                .build();
        testClaim2.setCreatedTime(now);
        testClaim2.setUpdatedTime(now);
    }

    @Test
    void testGetAllRebateClaims_WithFilters() {
        // Arrange
        Page<RebateClaim> mockPage = new PageImpl<>(Arrays.asList(testClaim1, testClaim2));
        when(rebateClaimRepository.findAllRebateClaimsWithFilters(
                eq("0030"), eq(RebateClaimStatus.MARKETING_VALIDATED), any(Pageable.class)))
                .thenReturn(mockPage);

        // Act
        PageableWsDTO pagination = PageableWsDTO.builder()
                .currentPage(0)
                .pageSize(30)
                .sort("createdTime,desc")
                .build();
        RebateClaimListWsDTO result = rebateClaimService.getAllRebateClaims(
                "0030", "MARKETING_VALIDATED", pagination);

        // Assert
        assertNotNull(result);
        assertEquals("rebateClaimSearchPageWsDTO", result.getType());
        assertEquals(2, result.getResults().size());
        assertEquals(0, result.getPagination().getCurrentPage());
        assertEquals(30, result.getPagination().getPageSize());
        assertEquals(2L, result.getPagination().getTotalResults());
    }

    @Test
    void testGetAllRebateClaims_WithoutFilters() {
        // Arrange
        Page<RebateClaim> mockPage = new PageImpl<>(Arrays.asList(testClaim1, testClaim2));
        when(rebateClaimRepository.findAllRebateClaimsWithFilters(
                eq(""), eq(null), any(Pageable.class)))
                .thenReturn(mockPage);

        // Act
        PageableWsDTO pagination = PageableWsDTO.builder()
                .currentPage(0)
                .pageSize(30)
                .build();
        RebateClaimListWsDTO result = rebateClaimService.getAllRebateClaims(
                "", "", pagination);

        // Assert
        assertNotNull(result);
        assertEquals(2, result.getResults().size());
    }

    @Test
    void testGetAllRebateClaims_EmptyResult() {
        // Arrange
        Page<RebateClaim> mockPage = new PageImpl<>(Arrays.asList());
        when(rebateClaimRepository.findAllRebateClaimsWithFilters(
                any(), any(), any(Pageable.class)))
                .thenReturn(mockPage);

        // Act
        PageableWsDTO pagination = PageableWsDTO.builder()
                .currentPage(0)
                .pageSize(30)
                .build();
        RebateClaimListWsDTO result = rebateClaimService.getAllRebateClaims(
                "9999", "CANCELLED", pagination);

        // Assert
        assertNotNull(result);
        assertEquals(0, result.getResults().size());
        assertEquals(0L, result.getPagination().getTotalResults());
    }

    @Test
    void testGetRebateClaimByCode_Found() {
        // Arrange
        when(rebateClaimRepository.findById("0030-CLM-001"))
                .thenReturn(Optional.of(testClaim1));

        // Act
        ClaimListItemWsDTO result = rebateClaimService.getRebateClaimByCode("0030-CLM-001");

        // Assert
        assertNotNull(result);
        assertEquals("0030-CLM-001", result.getId());
        assertEquals("0030", result.getDealer().getName());
        assertEquals("1000.00", result.getClaimableAmount().getValue());
        assertEquals("MARKETING_VALIDATED", result.getStatus().getCode());
    }

    @Test
    void testGetRebateClaimByCode_NotFound() {
        // Arrange
        when(rebateClaimRepository.findById("nonexistent"))
                .thenReturn(Optional.empty());

        // Act
        ClaimListItemWsDTO result = rebateClaimService.getRebateClaimByCode("nonexistent");

        // Assert
        assertNull(result);
    }

    @Test
    void testGetTotalRebateClaimsCount() {
        // Arrange
        when(rebateClaimRepository.countRebateClaimsWithFilters("0030", RebateClaimStatus.MARKETING_VALIDATED))
                .thenReturn(5L);

        // Act
        long result = rebateClaimService.getTotalRebateClaimsCount("0030", "MARKETING_VALIDATED");

        // Assert
        assertEquals(5L, result);
    }

    @Test
    void testGetTotalRebateClaimsCount_InvalidStatus() {
        // Arrange
        when(rebateClaimRepository.countRebateClaimsWithFilters("0030", null))
                .thenReturn(3L);

        // Act
        long result = rebateClaimService.getTotalRebateClaimsCount("0030", "INVALID_STATUS");

        // Assert
        assertEquals(3L, result);
    }
}
